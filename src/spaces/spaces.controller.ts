import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
} from '@nestjs/common';
import { SpacesService } from './spaces.service';
import { CreateSpaceDto } from './dto/create-space.dto';
import { CreateSpaceWithRoomsDto } from './dto/create-space-with-rooms.dto';
import { UpdateSpaceDto } from './dto/update-space.dto';
import { ClaimSpaceDto, ClaimSpaceResponseDto } from './dto/claim-space.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Space } from './domain/space';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllSpacesDto } from './dto/find-all-spaces.dto';
import { FindNearbySpacesDto } from './dto/find-nearby-spaces.dto';
import { RolesGuard } from '../roles/roles.guard';
import { RoleEnum } from '../roles/roles.enum';
import { Roles } from '../roles/roles.decorator';

@ApiTags('Spaces')
@Controller({
  path: 'spaces',
  version: '1',
})
export class SpacesController {
  constructor(private readonly spacesService: SpacesService) {}

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Post()
  @ApiCreatedResponse({
    type: Space,
  })
  create(
    @Body() createSpaceDto: CreateSpaceDto,
    @Request() req,
  ): Promise<Space> {
    return this.spacesService.create(createSpaceDto, req.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @Post('with-rooms')
  @ApiCreatedResponse({
    type: Space,
  })
  createWithRooms(
    @Body() createSpaceWithRoomsDto: CreateSpaceWithRoomsDto,
    @Request() req,
  ): Promise<Space> {
    console.log(
      'createSpaceWithRoomsDto from controller',
      createSpaceWithRoomsDto,
    );
    return this.spacesService.createSpaceWithRoomsAndImages(
      createSpaceWithRoomsDto,
      req.user,
    );
  }

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(Space),
  })
  async findAll(
    @Query() query: FindAllSpacesDto,
  ): Promise<InfinityPaginationResponseDto<Space>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 10;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.spacesService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get('nearby')
  @ApiOkResponse({
    type: [Space],
    description:
      'Returns spaces within the specified radius of the given point, ordered by distance',
  })
  findNearby(@Query() query: FindNearbySpacesDto): Promise<Space[]> {
    return this.spacesService.findNearby(
      query.latitude,
      query.longitude,
      query.radius,
      {
        priceMin: query.priceMin,
        priceMax: query.priceMax,
        rating: query.rating,
        spaceTypes: query.spaceTypes,
        amenities: query.amenities,
        hasMembership: query.hasMembership,
      },
    );
  }

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Get('owned')
  @ApiOkResponse({
    type: [Space],
  })
  getOwnedSpaces(@Request() req): Promise<Space[]> {
    return this.spacesService.getOwnedSpaces(req.user);
  }

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Get('owned/tasks')
  @ApiOkResponse({
    type: [Space],
  })
  getOwnedSpacesWithTasks(@Request() req): Promise<Space[]> {
    return this.spacesService.getOwnedSpacesWithTasks(req.user);
  }

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Get('owned/navigation')
  @ApiOkResponse({
    type: [Space],
  })
  getOwnedSpacesForNavigation(@Request() req): Promise<Space[]> {
    return this.spacesService.getOwnedSpacesForNavigation(req.user);
  }

  @Get('by-slug/:slug')
  @ApiParam({
    name: 'slug',
    type: String,
    required: true,
    description: 'The slug of the space',
  })
  @ApiOkResponse({
    type: Space,
    description: 'Returns a space by its slug',
  })
  findBySlug(@Param('slug') slug: string) {
    return this.spacesService.findBySlug(slug);
  }

  @Get('by-slug/:slug/details')
  @UseGuards(AuthGuard('anonymous'))
  @ApiParam({
    name: 'slug',
    type: String,
    required: true,
    description: 'The slug of the space',
  })
  @ApiOkResponse({
    type: Space,
    description: 'Returns a space by its slug with all related data',
  })
  findBySlugWithRelations(@Param('slug') slug: string) {
    return this.spacesService.findBySlugWithRelations(slug);
  }

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: string) {
    return this.spacesService.findOne(id);
  }

  @Get(':id/memberships')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
    description: 'The id of the space',
  })
  getSpaceMemberships(@Param('id') id: string) {
    return this.spacesService.getSpaceMemberships(id);
  }

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: Space,
  })
  update(@Param('id') id: string, @Body() updateSpaceDto: UpdateSpaceDto) {
    return this.spacesService.update(id, updateSpaceDto);
  }

  @ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.spacesService.remove(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @Post(':id/claim')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
    description: 'The ID of the space to claim',
  })
  @ApiCreatedResponse({
    type: ClaimSpaceResponseDto,
    description: 'Space claim request submitted successfully',
  })
  claimSpace(
    @Param('id') spaceId: string,
    @Body() claimSpaceDto: ClaimSpaceDto,
    @Request() req,
  ): Promise<ClaimSpaceResponseDto> {
    return this.spacesService.claimSpace(spaceId, claimSpaceDto, req.user);
  }
}
