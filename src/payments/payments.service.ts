import {
  Injectable,
  BadRequestException,
  HttpStatus,
  NotFoundException,
  UnprocessableEntityException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { PaymentRepository } from './infrastructure/persistence/payment.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { Payment } from './domain/payment';
import { PaymentMethodEnum } from './payment-method.enum';
import { StatusEnum } from './status.enum';
import { Transactional } from 'typeorm-transactional';
import { BookingsService } from '../bookings/bookings.service';
import { LoggerService } from '../infrastructure/logger/logger.service';
import { PaginateQuery } from 'nestjs-paginate';
import { User } from '../users/domain/user';
import { PaymentStrategyFactory } from './strategies/payment-strategy.factory';
import { Booking } from 'src/bookings/domain/booking';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OnEvent } from '@nestjs/event-emitter';
import {
  AttachInvoiceToPaymentEvent,
  GenerateInvoiceBeforePaymentEvent,
} from '../system-events/generate-invoice.event';

@Injectable()
export class PaymentsService {
  constructor(
    private readonly paymentRepository: PaymentRepository,
    @Inject(forwardRef(() => BookingsService))
    private readonly bookingsService: BookingsService,
    private readonly logger: LoggerService,
    private readonly paymentStrategyFactory: PaymentStrategyFactory,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.logger.setContext('PaymentsService');
  }

  async create(createPaymentDto: CreatePaymentDto): Promise<Payment> {
    this.logger.debug({
      message: 'Creating payment',
      createPaymentDto,
    });

    try {
      // Ensure all required fields have values
      const paymentData = {
        ...createPaymentDto,
        transactionId: createPaymentDto.transactionId || '',
        authorizationCode: createPaymentDto.authorizationCode || '',
        receiptNumber: createPaymentDto.receiptNumber || '',
        isRefund: createPaymentDto.isRefund || false,
        originalPaymentId: createPaymentDto.originalPaymentId || '',
        paymentProcessor: createPaymentDto.paymentProcessor || 'Manual',
        notes: createPaymentDto.notes || '',
        payerId:
          createPaymentDto.payerId ||
          (createPaymentDto.booking?.user?.id
            ? Number(createPaymentDto.booking.user.id)
            : undefined),
      };

      const payment = await this.paymentRepository.create(paymentData);
      this.logger.log({
        message: 'Payment created successfully',
        paymentId: payment.id,
      });
      return payment;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to create payment',
          error: error.message,
          createPaymentDto,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToCreatePayment',
        },
      });
    }
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    this.logger.debug({
      message: 'Fetching payments with pagination',
      page: paginationOptions.page,
      limit: paginationOptions.limit,
    });

    try {
      const results = await this.paymentRepository.findAllWithPagination({
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      });

      this.logger.log({
        message: 'Payments fetched successfully',
        count: results.length,
      });

      return results;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch payments with pagination',
          error: error.message,
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          pagination: 'failedToFetchPayments',
        },
      });
    }
  }

  async findOne(id: Payment['id']) {
    this.logger.debug({
      message: 'Fetching payment by ID',
      paymentId: id,
    });

    try {
      const payment = await this.paymentRepository.findById(id);
      if (payment) {
        this.logger.log({
          message: 'Payment found',
          paymentId: id,
        });
        return payment;
      } else {
        this.logger.warn({
          message: 'Payment not found',
          paymentId: id,
        });
        throw new NotFoundException({
          status: HttpStatus.NOT_FOUND,
          errors: {
            payment: 'paymentNotFound',
          },
        });
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(
        {
          message: 'Error fetching payment by ID',
          error: error.message,
          paymentId: id,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToFetchPayment',
        },
      });
    }
  }

  async update(id: Payment['id'], updatePaymentDto: UpdatePaymentDto) {
    this.logger.debug({
      message: 'Updating payment',
      paymentId: id,
      updateFields: Object.keys(updatePaymentDto),
    });

    try {
      const existingPayment = await this.paymentRepository.findById(id);
      if (!existingPayment) {
        this.logger.warn({
          message: 'Payment not found for update',
          paymentId: id,
        });
        throw new NotFoundException({
          status: HttpStatus.NOT_FOUND,
          errors: {
            payment: 'paymentNotFound',
          },
        });
      }

      const updatedPayment = await this.paymentRepository.update(
        id,
        updatePaymentDto,
      );
      this.logger.log({
        message: 'Payment updated successfully',
        paymentId: id,
      });
      return updatedPayment;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(
        {
          message: 'Failed to update payment',
          error: error.message,
          paymentId: id,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToUpdatePayment',
        },
      });
    }
  }

  async remove(id: Payment['id']) {
    this.logger.debug({
      message: 'Removing payment',
      paymentId: id,
    });

    try {
      const existingPayment = await this.paymentRepository.findById(id);
      if (!existingPayment) {
        this.logger.warn({
          message: 'Payment not found for removal',
          paymentId: id,
        });
        throw new NotFoundException({
          status: HttpStatus.NOT_FOUND,
          errors: {
            payment: 'paymentNotFound',
          },
        });
      }

      const result = await this.paymentRepository.remove(id);
      this.logger.log({
        message: 'Payment removed successfully',
        paymentId: id,
      });
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(
        {
          message: 'Failed to remove payment',
          error: error.message,
          paymentId: id,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToRemovePayment',
        },
      });
    }
  }

  @Transactional()
  async handleBookingPayment(
    bookingId: string,
    method: string,
    processedById?: number,
  ) {
    const correlationId =
      Date.now().toString(36) + Math.random().toString(36).substr(2);

    // Ensure processedById is always set to a valid number
    const finalProcessedById = processedById || 1;

    this.logger.debug({
      message: 'Handling payment for booking',
      bookingId,
      method,
      processedById: finalProcessedById,
      correlationId,
    });

    try {
      // Validate payment method
      if (
        !Object.values(PaymentMethodEnum).includes(method as PaymentMethodEnum)
      ) {
        this.logger.warn({
          message: 'Invalid payment method',
          bookingId,
          method,
          correlationId,
        });
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          errors: {
            method: 'invalidPaymentMethod',
          },
        });
      }

      // Check if booking exists
      const booking = await this.bookingsService.findOne(bookingId);
      if (!booking) {
        this.logger.warn({
          message: 'Booking not found',
          bookingId,
          correlationId,
        });
        throw new NotFoundException({
          status: HttpStatus.NOT_FOUND,
          errors: {
            booking: 'bookingNotFound',
          },
        });
      }

      // Get the appropriate payment strategy
      const strategy = this.paymentStrategyFactory.getStrategy(
        method as PaymentMethodEnum,
        this.bookingsService,
      );

      // Execute the payment strategy
      const result = await strategy.handle({
        booking,
        correlationId,
        processedById: finalProcessedById,
      });

      this.logger.log({
        message: 'Payment handled successfully',
        bookingId,
        method,
        paymentId: result.paymentId,
        correlationId,
      });

      return {
        booking,
        result,
        shouldEmitEvent: method === PaymentMethodEnum.TRANSFER,
      };
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to handle booking payment',
          error: error.message,
          bookingId,
          method,
          correlationId,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToProcessPayment',
        },
      });
    }
  }

  /**
   * Complete checkout session and process payment
   * This is the main method called when user selects payment method
   */
  @Transactional()
  async completeCheckoutAndProcessPayment(
    bookingId: string,
    method: string,
    processedById?: number,
  ) {
    this.logger.debug({
      message: 'Completing checkout and processing payment',
      bookingId,
      method,
      processedById,
    });

    try {
      // Get current booking to check status
      const currentBooking = await this.bookingsService.findOne(bookingId);
      if (!currentBooking) {
        throw new Error('Booking not found');
      }

      // Step 1: Complete checkout session only if booking is in CHECKOUT_PENDING status
      if (currentBooking.status === 'checkout_pending') {
        await this.bookingsService.completeCheckoutSession(bookingId, method);
      }
      // If booking is already in AWAITING_PAYMENT or PENDING_APPROVAL, skip checkout completion

      // Step 2: Process payment and handle approval if needed
      const { booking, result, shouldEmitEvent } =
        await this.handleBookingPayment(bookingId, method, processedById);

      // Step 3: Generate invoice if needed
      if (shouldEmitEvent) {
        const generateInvoiceEvent = new GenerateInvoiceBeforePaymentEvent(
          booking,
          result,
        );
        this.eventEmitter.emit('booking-payment-success', generateInvoiceEvent);
      }

      this.logger.log({
        message: 'Checkout and payment completed successfully',
        bookingId,
        method,
        paymentId: result.paymentId,
      });

      return result;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to complete checkout and process payment',
          bookingId,
          method,
          error: error.message,
        },
        error.stack,
      );
      throw error;
    }
  }

  async payBookingAndGenerateInvoice(
    bookingId: string,
    method: string,
    processedById?: number,
  ) {
    const { booking, result, shouldEmitEvent } =
      await this.handleBookingPayment(bookingId, method, processedById);

    if (shouldEmitEvent) {
      const generateInvoiceEvent = new GenerateInvoiceBeforePaymentEvent(
        booking,
        result,
      );
      this.eventEmitter.emit(
        'generate-invoice-before-payment',
        generateInvoiceEvent,
      );
    }

    return result;
  }

  /**
   * Confirm a pending booking payment (for cash/bank transfer)
   * This is used by staff to manually confirm payments from the pending payments page
   */
  async confirmBookingPayment(
    paymentId: string,
    staffId: string | number,
  ): Promise<any> {
    this.logger.debug({
      message: 'Confirming booking payment',
      paymentId,
      staffId,
    });

    try {
      // Get the payment to check if it exists and is pending
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment) {
        throw new NotFoundException({
          status: HttpStatus.NOT_FOUND,
          errors: {
            payment: 'paymentNotFound',
          },
        });
      }

      // Check if payment is in a confirmable state
      if (payment.status !== 'pending' && payment.status !== 'waiting_for_cash') {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          errors: {
            payment: 'paymentNotPending',
          },
        });
      }

      // Update payment status to completed
      await this.paymentRepository.update(paymentId, {
        status: StatusEnum.COMPLETED,
        processedById: typeof staffId === 'string' ? parseInt(staffId) : staffId,
        paymentDate: new Date(),
        notes: `${payment.notes || ''} - Confirmed manually by staff`.trim(),
      });

      // Get the booking and try to confirm it if all requirements are met
      if (payment.booking) {
        await this.bookingsService.tryConfirmBooking(payment.booking.id);
      }

      this.logger.log({
        message: 'Booking payment confirmed successfully',
        paymentId,
        staffId,
        bookingId: payment.booking?.id,
      });

      return {
        success: true,
        message: 'Payment confirmed successfully',
      };
    } catch (error) {
      this.logger.error({
        message: 'Failed to confirm booking payment',
        paymentId,
        error: error.message,
      });

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to confirm payment: ${error.message}`,
      );
    }
  }

  findAllPaginatedFilteredAndSorted(query: PaginateQuery, userId: User['id']) {
    this.logger.debug({
      message: 'Fetching paginated payments for user',
      userId,
      query,
    });

    try {
      return this.paymentRepository.findAllPaginatedFilteredAndSorted(
        query,
        userId,
      );
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch paginated payments',
          error: error.message,
          userId,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToFetchPayments',
        },
      });
    }
  }

  findPaymentsByBookingId(bookingId: Booking['id']) {
    return this.paymentRepository.findPaymentsByBookingId(bookingId);
  }

  async findPendingMembershipPaymentsBySpace(spaceId: string) {
    this.logger.debug({
      message: 'Fetching pending membership payments for space',
      spaceId,
    });

    try {
      const payments =
        await this.paymentRepository.findPendingMembershipPaymentsBySpace(
          spaceId,
        );

      this.logger.log({
        message: 'Pending membership payments fetched successfully',
        spaceId,
        count: payments.length,
      });

      return payments;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch pending membership payments',
          error: error.message,
          spaceId,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToFetchPendingMembershipPayments',
        },
      });
    }
  }

  async findPendingBookingPaymentsBySpace(spaceId: string) {
    this.logger.debug({
      message: 'Fetching pending booking payments for space',
      spaceId,
    });

    try {
      // Get actual payment records with pending or waiting_for_cash status for the space
      const payments = await this.paymentRepository.findPendingBookingPaymentsBySpace(spaceId);

      this.logger.log({
        message: 'Pending booking payments fetched successfully',
        spaceId,
        count: payments.length,
      });

      return payments;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch pending booking payments',
          error: error.message,
          spaceId,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToFetchPendingBookingPayments',
        },
      });
    }
  }

  @OnEvent('attachInvoiceToPayment')
  async handleAttachInvoiceToPayment(event: AttachInvoiceToPaymentEvent) {
    const { invoice, paymentId } = event;
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      this.logger.warn({
        message: 'Payment not found',
        paymentId,
      });
      throw new NotFoundException({
        status: HttpStatus.NOT_FOUND,
        errors: {
          payment: 'paymentNotFound',
        },
      });
    }
    payment.invoice = invoice;
    await this.paymentRepository.update(paymentId, payment);
    this.logger.log({
      message: 'Invoice attached to payment',
      invoiceId: invoice.id,
      paymentId,
    });
  }

  /**
   * Get today's revenue for a space
   */
  async getTodayRevenue(spaceId: string): Promise<number> {
    try {
      const today = new Date();
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
      );
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const payments =
        await this.paymentRepository.findPaymentsBySpaceAndDateRange(
          spaceId,
          startOfDay,
          endOfDay,
        );

      return payments.reduce((sum, payment) => sum + payment.amount, 0);
    } catch (error) {
      this.logger.error({
        message: 'Error getting today revenue',
        spaceId,
        error: error.message,
      });
      return 0;
    }
  }

  /**
   * Get payment analytics for a space within a date range
   */
  async getPaymentAnalytics(spaceId: string, startDate: Date, endDate: Date) {
    try {
      const payments =
        await this.paymentRepository.findPaymentsBySpaceAndDateRange(
          spaceId,
          startDate,
          endDate,
        );

      const totalRevenue = payments.reduce(
        (sum, payment) => sum + payment.amount,
        0,
      );
      const totalPayments = payments.length;
      const averageTransactionValue =
        totalPayments > 0 ? totalRevenue / totalPayments : 0;

      // Group by payment method
      const paymentMethods = new Map<
        string,
        { count: number; amount: number }
      >();
      payments.forEach((payment) => {
        const method = payment.paymentMethod;
        const existing = paymentMethods.get(method) || { count: 0, amount: 0 };
        existing.count += 1;
        existing.amount += payment.amount;
        paymentMethods.set(method, existing);
      });

      const paymentMethodsArray = Array.from(paymentMethods.entries()).map(
        ([method, data]) => ({
          method,
          count: data.count,
          amount: data.amount,
          percentage:
            totalPayments > 0 ? (data.count / totalPayments) * 100 : 0,
        }),
      );

      // Group by month for revenue trend
      const revenueByMonth = new Map<
        string,
        { revenue: number; bookings: number }
      >();
      payments.forEach((payment) => {
        const month = payment.paymentDate.toISOString().substring(0, 7); // YYYY-MM
        const existing = revenueByMonth.get(month) || {
          revenue: 0,
          bookings: 0,
        };
        existing.revenue += payment.amount;
        existing.bookings += 1;
        revenueByMonth.set(month, existing);
      });

      const revenueByMonthArray = Array.from(revenueByMonth.entries()).map(
        ([month, data]) => ({
          month,
          revenue: data.revenue,
          bookings: data.bookings,
        }),
      );

      return {
        totalRevenue,
        totalPayments,
        averageTransactionValue,
        revenueByMonth: revenueByMonthArray,
        paymentMethods: paymentMethodsArray,
      };
    } catch (error) {
      this.logger.error({
        message: 'Error calculating payment analytics',
        spaceId,
        error: error.message,
      });
      throw error;
    }
  }

  async findAllMembershipPaymentsBySpace(spaceId: string) {
    this.logger.debug({
      message: 'Fetching all membership payments for space',
      spaceId,
    });

    try {
      const payments =
        await this.paymentRepository.findAllMembershipPaymentsBySpace(spaceId);

      this.logger.log({
        message: 'All membership payments fetched successfully',
        spaceId,
        count: payments.length,
      });

      return payments;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch all membership payments',
          error: error.message,
          spaceId,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToFetchAllMembershipPayments',
        },
      });
    }
  }
}
