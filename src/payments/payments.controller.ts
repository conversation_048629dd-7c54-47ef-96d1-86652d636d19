import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  SerializeOptions,
} from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { MembershipPaymentService } from '../space-members/membership-payment.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Payment } from './domain/payment';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllPaymentsDto } from './dto/find-all-payments.dto';
import {
  PaginateQuery,
  Paginated,
  PaginatedSwaggerDocs,
} from 'nestjs-paginate';
import { paymentPaginationConfig } from './infrastructure/persistence/relational/config/pagination.config';

@ApiTags('Payments')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'payments',
  version: '1',
})
export class PaymentsController {
  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly membershipPaymentService: MembershipPaymentService,
  ) {}

  @Post()
  @ApiCreatedResponse({
    type: Payment,
  })
  create(@Body() createPaymentDto: CreatePaymentDto) {
    return this.paymentsService.create(createPaymentDto);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(Payment),
  })
  async findAll(
    @Query() query: FindAllPaymentsDto,
  ): Promise<InfinityPaginationResponseDto<Payment>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 10;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.paymentsService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: string) {
    return this.paymentsService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: Payment,
  })
  update(@Param('id') id: string, @Body() updatePaymentDto: UpdatePaymentDto) {
    return this.paymentsService.update(id, updatePaymentDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.paymentsService.remove(id);
  }

  @Post('booking/:bookingId/pay')
  @ApiParam({
    name: 'bookingId',
    type: String,
    required: true,
    description: 'ID of the booking to process payment for',
  })
  @ApiCreatedResponse({
    description: 'Payment processed successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        message: { type: 'string' },
        paymentId: { type: 'string' },
      },
    },
  })
  async handleBookingPayment(
    @Param('bookingId') bookingId: string,
    @Body('method') method: string,
    @Body('processedById') processedById: number,
    @Request() request: any,
  ) {
    // If processedById is not provided, use the logged-in user's ID
    const staffId = processedById || request.user?.id || 1; // Ensure we always have a valid ID

    // First complete checkout session (CHECKOUT_PENDING → AWAITING_PAYMENT)
    const result = await this.paymentsService.completeCheckoutAndProcessPayment(
      bookingId,
      method,
      staffId,
    );

    return {
      status: 'success',
      message: 'Payment processed successfully',
      bookingId: bookingId,
      paymentId: result.paymentId || null,
    };
  }

  @SerializeOptions({
    groups: ['me'],
  })
  @Get('user/all')
  @PaginatedSwaggerDocs(Payment, paymentPaginationConfig)
  async findAllPaginatedFilteredAndSorted(
    @Query() query: PaginateQuery,
    @Request() request,
  ): Promise<Paginated<Payment>> {
    return this.paymentsService.findAllPaginatedFilteredAndSorted(
      query,
      request.user.id,
    );
  }

  @Post('membership/:membershipId/pay')
  @ApiParam({
    name: 'membershipId',
    type: String,
    required: true,
    description: 'ID of the membership to process payment for',
  })
  @ApiCreatedResponse({
    description: 'Membership payment processed successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        message: { type: 'string' },
        paymentId: { type: 'string' },
      },
    },
  })
  async handleMembershipPayment(
    @Param('membershipId') membershipId: string,
    @Body('method') method: string,
    @Body('processedById') processedById: number,
    @Request() request: any,
  ) {
    try {
      // If processedById is not provided, use the logged-in user's ID
      const staffId = processedById || request.user?.id || 1;

      const result =
        await this.membershipPaymentService.processMembershipPayment(
          membershipId,
          method as any, // Cast to PaymentMethodEnum
          staffId,
        );

      return {
        status: 'success',
        message: 'Membership payment processed successfully',
        paymentId: result.payment?.id || 'simulated',
        data: result,
      };
    } catch (error) {
      return {
        status: 'error',
        message: error.message || 'Payment processing failed',
      };
    }
  }

  @Patch('membership/:paymentId/confirm')
  @ApiParam({
    name: 'paymentId',
    type: String,
    required: true,
    description: 'ID of the payment to confirm',
  })
  @ApiOkResponse({
    description: 'Payment confirmed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async confirmMembershipPayment(
    @Param('paymentId') paymentId: string,
    @Request() request: any,
  ) {
    try {
      const result =
        await this.membershipPaymentService.confirmMembershipPayment(
          paymentId,
          request.user?.id || 1,
        );

      return result;
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to confirm payment',
      };
    }
  }

  @Patch('booking/:paymentId/confirm')
  @ApiParam({
    name: 'paymentId',
    type: String,
    required: true,
    description: 'ID of the booking payment to confirm',
  })
  @ApiOkResponse({
    description: 'Booking payment confirmed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async confirmBookingPayment(
    @Param('paymentId') paymentId: string,
    @Request() request: any,
  ) {
    try {
      const result = await this.paymentsService.confirmBookingPayment(
        paymentId,
        request.user?.id || 1,
      );

      return result;
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to confirm booking payment',
      };
    }
  }

  @Get('booking/pending/space/:spaceId')
  @ApiParam({
    name: 'spaceId',
    type: String,
    required: true,
    description: 'ID of the space to get pending booking payments for',
  })
  @ApiOkResponse({
    description: 'Get pending booking payments for a specific space',
  })
  async getPendingBookingPaymentsBySpace(@Param('spaceId') spaceId: string) {
    try {
      const payments =
        await this.paymentsService.findPendingBookingPaymentsBySpace(spaceId);

      return {
        success: true,
        data: payments,
        count: payments.length,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to fetch pending booking payments',
        data: [],
        count: 0,
      };
    }
  }

  @Get('membership/pending/space/:spaceId')
  @ApiParam({
    name: 'spaceId',
    type: String,
    required: true,
    description: 'ID of the space to get pending membership payments for',
  })
  @ApiOkResponse({
    type: [Payment],
    description: 'Get pending membership payments for a specific space',
  })
  async getPendingMembershipPaymentsBySpace(@Param('spaceId') spaceId: string) {
    try {
      const payments =
        await this.paymentsService.findPendingMembershipPaymentsBySpace(
          spaceId,
        );

      return {
        success: true,
        data: payments,
        count: payments.length,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to fetch pending membership payments',
        data: [],
        count: 0,
      };
    }
  }

  @Get('membership/debug/space/:spaceId')
  @ApiParam({
    name: 'spaceId',
    type: String,
    required: true,
    description: 'ID of the space to debug all membership payments',
  })
  @ApiOkResponse({
    type: [Payment],
    description: 'Get all membership payments for debugging',
  })
  async debugMembershipPaymentsBySpace(@Param('spaceId') spaceId: string) {
    try {
      const allPayments =
        await this.paymentsService.findAllMembershipPaymentsBySpace(spaceId);
      const pendingPayments =
        await this.paymentsService.findPendingMembershipPaymentsBySpace(
          spaceId,
        );

      return {
        success: true,
        data: {
          all: allPayments,
          pending: pendingPayments,
          summary: {
            totalCount: allPayments.length,
            pendingCount: pendingPayments.length,
            statusBreakdown: allPayments.reduce(
              (acc, payment) => {
                acc[payment.status] = (acc[payment.status] || 0) + 1;
                return acc;
              },
              {} as Record<string, number>,
            ),
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to debug membership payments',
        data: null,
      };
    }
  }

  @Get('booking/:bookingId')
  @ApiParam({
    name: 'bookingId',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: [Payment],
  })
  findPaymentsByBookingId(@Param('bookingId') bookingId: string) {
    return this.paymentsService.findPaymentsByBookingId(bookingId);
  }
}
