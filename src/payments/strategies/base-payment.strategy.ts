import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PaymentRepository } from '../infrastructure/persistence/payment.repository';
import { BookingsService } from '../../bookings/bookings.service';
import {
  IPaymentStrategy,
  PaymentContext,
  PaymentResult,
} from './payment-strategy.interface';
import { StatusEnum } from '../status.enum';
import { Payment } from '../domain/payment';
import { PaymentMethodEnum } from '../payment-method.enum';
import { UnprocessableEntityException, HttpStatus } from '@nestjs/common';

export abstract class BasePaymentStrategy implements IPaymentStrategy {
  constructor(
    protected readonly paymentRepository: PaymentRepository,
    protected readonly bookingsService: BookingsService,
    protected readonly logger: LoggerService,
    protected readonly paymentMethod: PaymentMethodEnum,
  ) {
    this.logger.setContext(this.constructor.name);
  }

  abstract handle(context: PaymentContext): Promise<PaymentResult>;

  protected async createOrUpdatePaymentRecord(
    context: PaymentContext,
    additionalData: Partial<Payment> = {},
  ): Promise<Payment> {
    try {
      // Check if there's already a completed payment for this booking with the same payment method
      // This prevents creating duplicate payments when confirming from pending payments page
      const existingPayments = await this.paymentRepository.findPaymentsByBookingId(context.booking.id);

      this.logger.debug({
        message: 'Checking existing payments for booking',
        bookingId: context.booking.id,
        paymentMethod: this.paymentMethod,
        existingPaymentsCount: existingPayments.length,
        existingPayments: existingPayments.map(p => ({ id: p.id, method: p.paymentMethod, status: p.status })),
      });

      // Check if there's already ANY completed payment for this booking (regardless of payment method)
      // A booking should only have one successful payment
      const existingCompletedPayment = existingPayments.find(
        payment =>
          (payment.status === StatusEnum.COMPLETED || payment.status === StatusEnum.SUCCESS)
      );

      if (existingCompletedPayment) {
        // Payment already exists and is completed - return it instead of creating duplicate
        this.logger.log({
          message: 'Payment already completed for this booking',
          paymentId: existingCompletedPayment.id,
          bookingId: context.booking.id,
          existingPaymentMethod: existingCompletedPayment.paymentMethod,
          requestedPaymentMethod: this.paymentMethod,
        });

        return existingCompletedPayment;
      }

      // Check for pending payments that can be updated (prefer same payment method, but accept any)
      const existingPendingPayment = existingPayments.find(
        payment =>
          (payment.status === StatusEnum.PENDING || payment.status === StatusEnum.WAITING_FOR_CASH)
      );

      if (existingPendingPayment) {
        // Update existing pending payment to completed with the requested payment method
        this.logger.log({
          message: 'Updating existing pending payment record',
          paymentId: existingPendingPayment.id,
          bookingId: context.booking.id,
          oldPaymentMethod: existingPendingPayment.paymentMethod,
          newPaymentMethod: this.paymentMethod,
        });

        const updatedPayment = await this.paymentRepository.update(existingPendingPayment.id, {
          status: StatusEnum.COMPLETED,
          paymentMethod: this.paymentMethod, // Update to the confirmed payment method
          paymentDate: new Date(),
          processedById: context.processedById,
          paymentProcessor: additionalData.paymentProcessor || this.constructor.name,
          transactionId: additionalData.transactionId || existingPendingPayment.transactionId,
          authorizationCode: additionalData.authorizationCode || existingPendingPayment.authorizationCode,
          receiptNumber: additionalData.receiptNumber || existingPendingPayment.receiptNumber,
          notes: additionalData.notes || existingPendingPayment.notes,
        });

        return updatedPayment!;
      }

      // Create new payment record with appropriate status based on context
      const payment = await this.paymentRepository.create({
        booking: context.booking,
        paymentMethod: this.paymentMethod,
        status: additionalData.status || StatusEnum.PENDING, // Use provided status or PENDING as default
        amount: context.booking.totalPrice,
        paymentDate: new Date(),
        processedById: context.processedById,
        payerId: context.booking.user?.id
          ? Number(context.booking.user.id)
          : undefined,
        paymentProcessor:
          additionalData.paymentProcessor || this.constructor.name,
        transactionId: additionalData.transactionId || '',
        authorizationCode: additionalData.authorizationCode || '',
        receiptNumber: additionalData.receiptNumber || '',
        isRefund: additionalData.isRefund || false,
        originalPaymentId: additionalData.originalPaymentId || '',
        notes: additionalData.notes || '',
      });

      this.logger.log({
        message: 'Payment record created successfully',
        paymentId: payment.id,
        bookingId: context.booking.id,
        paymentMethod: this.paymentMethod,
        correlationId: context.correlationId,
      });

      return payment;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to create or update payment record',
          error: error.message,
          bookingId: context.booking.id,
          paymentMethod: this.paymentMethod,
          correlationId: context.correlationId,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          payment: 'failedToCreateOrUpdatePaymentRecord',
        },
      });
    }
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use createOrUpdatePaymentRecord instead
   */
  protected async createPaymentRecord(
    context: PaymentContext,
    additionalData: Partial<Payment> = {},
  ): Promise<Payment> {
    return this.createOrUpdatePaymentRecord(context, additionalData);
  }

  protected async processBookingPayment(
    context: PaymentContext,
    payment: Payment,
  ): Promise<void> {
    try {
      await this.bookingsService.processBookingAfterPayment(
        context.booking,
        payment,
      );
      this.logger.log({
        message: 'Booking processed after payment',
        bookingId: context.booking.id,
        paymentId: payment.id,
        correlationId: context.correlationId,
      });
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to process booking after payment',
          error: error.message,
          bookingId: context.booking.id,
          paymentId: payment.id,
          correlationId: context.correlationId,
        },
        error.stack,
      );
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: {
          booking: 'failedToProcessBookingAfterPayment',
        },
      });
    }
  }
}
