import { Injectable } from '@nestjs/common';
import { BasePaymentStrategy } from './base-payment.strategy';
import { PaymentContext, PaymentResult } from './payment-strategy.interface';
import { PaymentMethodEnum } from '../payment-method.enum';
import { StatusEnum } from '../status.enum';
import { PaymentRepository } from '../infrastructure/persistence/payment.repository';
import { BookingsService } from '../../bookings/bookings.service';
import { LoggerService } from '../../infrastructure/logger/logger.service';

@Injectable()
export class TransferPaymentStrategy extends BasePaymentStrategy {
  constructor(
    paymentRepository: PaymentRepository,
    bookingsService: BookingsService,
    logger: LoggerService,
  ) {
    super(
      paymentRepository,
      bookingsService,
      logger,
      PaymentMethodEnum.TRANSFER,
    );
  }

  async handle(context: PaymentContext): Promise<PaymentResult> {
    this.logger.debug({
      message: 'Handling transfer payment',
      bookingId: context.booking.id,
      correlationId: context.correlationId,
    });

    // Generate reference number for bank transfers
    const referenceNumber = `TRF-${Date.now().toString().slice(-6)}-${Math.floor(
      Math.random() * 1000,
    )
      .toString()
      .padStart(3, '0')}`;

    const payment = await this.createOrUpdatePaymentRecord(context, {
      receiptNumber: referenceNumber,
      paymentProcessor: 'Bank Transfer',
      notes: 'Bank transfer payment - pending verification',
      // No status override - will use default PENDING status
    });

    await this.processBookingPayment(context, payment);

    return {
      status: 'success',
      message: 'Transfer payment initiated successfully',
      bookingId: context.booking.id,
      paymentId: payment.id,
      referenceNumber,
    };
  }
}
