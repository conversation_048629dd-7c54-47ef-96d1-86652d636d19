import FetchFactory from '../factory'
import type { BookingPaymentResponse, PaymentPaginatedResponse, PaymentPaginatedParams } from '~/interfaces/payment'
import type { Payment } from '~/types'

/**
 * The PaymentModule class is responsible for making API requests to the payment endpoints.
 */

class PaymentModule extends FetchFactory<BookingPaymentResponse | PaymentPaginatedResponse | Payment[]> {
  private readonly RESOURCE = '/payments'

  async initiateBookingPayment(bookingId: string, method: string): Promise<BookingPaymentResponse> {
    return this.call('POST', `${this.RESOURCE}/booking/${bookingId}/pay`, { method }) as Promise<BookingPaymentResponse>;
  }

  async getPayments(params?: PaymentPaginatedParams): Promise<PaymentPaginatedResponse> {
    const queryString = this.serializeParams(params);
    const url = `${this.RESOURCE}/user/all${queryString ? `?${queryString}` : ''}`;
    return this.call('GET', url) as Promise<PaymentPaginatedResponse>;
  }

  async getPaymentsByBookingId(bookingId: string): Promise<Payment[]> {
    return this.call('GET', `${this.RESOURCE}/booking/${bookingId}`) as Promise<Payment[]>;
  }

  async initiateMembershipPayment(membershipId: string, method: string): Promise<BookingPaymentResponse> {
    return this.call('POST', `${this.RESOURCE}/membership/${membershipId}/pay`, { method }) as Promise<BookingPaymentResponse>;
  }

  async getPendingBookingPaymentsBySpace(spaceId: string): Promise<{ success: boolean; data: any[]; count: number; message?: string }> {
    return this.call('GET', `${this.RESOURCE}/booking/pending/space/${spaceId}`) as Promise<{ success: boolean; data: any[]; count: number; message?: string }>;
  }

  async getPendingMembershipPaymentsBySpace(spaceId: string): Promise<{ success: boolean; data: Payment[]; count: number; message?: string }> {
    return this.call('GET', `${this.RESOURCE}/membership/pending/space/${spaceId}`) as Promise<{ success: boolean; data: Payment[]; count: number; message?: string }>;
  }

  async debugMembershipPaymentsBySpace(spaceId: string): Promise<{ success: boolean; data: any; message?: string }> {
    return this.call('GET', `${this.RESOURCE}/membership/debug/space/${spaceId}`) as Promise<{ success: boolean; data: any; message?: string }>;
  }

  async confirmBookingPayment(paymentId: string): Promise<{ success: boolean; message: string }> {
    return this.call('PATCH', `${this.RESOURCE}/booking/${paymentId}/confirm`) as Promise<{ success: boolean; message: string }>;
  }

  private serializeParams(params?: { [key: string]: any }): string {
    if (!params) return '';
    const query = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach((val) => query.append(key, val));
      } else if (value !== undefined && value !== null) {
        query.append(key, String(value));
      }
    });

    return query.toString();
  }
}

export default PaymentModule